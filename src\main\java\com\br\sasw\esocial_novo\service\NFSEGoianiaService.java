package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.util.NFSeGoianiaXmlSigner;
import lombok.RequiredArgsConstructor;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import java.nio.charset.StandardCharsets;

@Service
@RequiredArgsConstructor
public class NFSEGoianiaService {

    @Value("${nfse.goiania.url}")
    private String url;

    @Value("${nfse.goiania.envio.action}")
    private String envioAction;

    @Value("${nfse.goiania.consulta.action}")
    private String consultaAction;

    private final HttpClientService httpClientService;

    public String send(String xml, String empresa) {

        String xmlAssinado = NFSeGoianiaXmlSigner.sign(xml, empresa);

        String soapEnv = String.format("<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:ws=\"http://nfse.goiania.go.gov.br/ws/\">\n" +
                "    <soap:Header/>\n" +
                "    <soap:Body>\n" +
                "        <ws:GerarNfse>\n" +
                "            <ws:ArquivoXML><![CDATA[%s]]></ws:ArquivoXML>\n" +
                "        </ws:GerarNfse>\n" +
                "    </soap:Body>\n" +
                "</soap:Envelope>", xmlAssinado);

        try {

            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);
            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("SOAPAction", envioAction);
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            byte[] xmlBytes = soapEnv.getBytes(StandardCharsets.UTF_8);
            int contentLength = xmlBytes.length;
            httpPost.setHeader("Content-Length", String.valueOf(contentLength));

            StringEntity entity = new StringEntity(soapEnv, "UTF-8");
            httpPost.setEntity(entity);

            CloseableHttpResponse resp = httpClient.execute(httpPost);

            return resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";
        } catch(Exception e) {
            return "Não foi possível enviar a NFSE";
        }
    }

    public String get(String xml, String empresa) {

        String soapEnv = String.format("<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" \n" +
                "               xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" \n" +
                "               xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"\n" +
                "               xmlns:ws=\"http://nfse.goiania.go.gov.br/ws/\">\n" +
                "    <soap:Body>\n" +
                "        <ws:ConsultarNfseRps>\n" +
                "            <ws:ArquivoXML><![CDATA[%s]]></ws:ArquivoXML>\n" +
                "        </ws:ConsultarNfseRps>\n" +
                "    </soap:Body>\n" +
                "</soap:Envelope>", xml);

        try {

            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);
            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("SOAPAction", consultaAction);
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            byte[] xmlBytes = soapEnv.getBytes(StandardCharsets.UTF_8);
            int contentLength = xmlBytes.length;
            httpPost.setHeader("Content-Length", String.valueOf(contentLength));

            StringEntity entity = new StringEntity(soapEnv, "UTF-8");
            httpPost.setEntity(entity);

            CloseableHttpResponse resp = httpClient.execute(httpPost);

            return resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";
        } catch(Exception e) {
            return "Não foi possível enviar a NFSE";
        }
    }

}
