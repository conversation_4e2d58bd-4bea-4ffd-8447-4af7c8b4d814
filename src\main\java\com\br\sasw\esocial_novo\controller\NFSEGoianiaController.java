package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.service.NFSEGoianiaService;
import com.br.sasw.esocial_novo.util.NFSeGoianiaXmlSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/nfse-goiania")
@RequiredArgsConstructor
public class NFSEGoianiaController {

    private final NFSEGoianiaService service;

    @PostMapping("/envio")
    public String send(@RequestBody String xml, @RequestParam String empresa) {
        return service.send(xml, empresa);
    }

    @PostMapping("/consulta")
    public String get(@RequestBody String xml, @RequestParam String empresa) {
        return service.get(xml, empresa);
    }
}
